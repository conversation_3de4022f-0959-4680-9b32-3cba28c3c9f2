/* eslint-disable no-console */
// client.js

// ================== DOM ELEMENTS & GLOBAL STATE ==================
const chatLog = document.getElementById('chatLog');
const clearBtn = document.getElementById('clearBtn');
const mcpServerStatus = document.getElementById('mcpServerStatus');
const queryInput = document.getElementById('queryInput');
const reconnectBtn = document.getElementById('reconnectBtn');
const sendBtn = document.getElementById('sendBtn');
const statusIcon = document.getElementById('statusIcon');

let connectionAttempts = 0;
const maxAttempts = 3; // Try for up to 2 minutes (12 * 10 seconds)
const retryDelay = 10000; // 10 seconds between attempts

// Add status message constants
const STATUS = {
    CONNECTED: 'Connected',
    CONNECTING: 'Connecting',
    FAILED: 'Connection failed',
    FAILED_TIMEOUT: 'Failed to connect after multiple attempts',
};

// 如果是用户消息，添加输入中状态
const loadingRow = document.createElement('div');
loadingRow.className = 'message-row';
loadingRow.innerHTML = `
    <div class="bubble loading">
        <div class="typing-indicator">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    </div>
`;
let firstMessage = true;

let curBubble = document.createElement('div');  // 流式消息bubble
let isCurBubbleInit = false;

// ================== SSE CONNECTION SETUP ==================
const eventSource = new EventSource('/sec-opt-agent/sse');

// Handle incoming SSE messages
eventSource.onmessage = (event) => {
    let data;
    try {
        data = JSON.parse(event.data);
    } catch {
        console.warn('Could not parse event as JSON:', event.data);
        return;
    }

    switch (data.type) {
        case 'status':
            // 处理状态更新
            if (data.taskInProgress !== undefined) {
                setSendButtonState(!data.taskInProgress);
            }
            // 处理初始状态中的历史消息
            if (data.messages) {
                data.messages.forEach(msg => {
                    appendMessage(msg.role, msg.content);
                });
            }
            break;
        case 'message':
            // 处理普通消息
            if (data.role === "assistant" && data.isPartial === true) {
                appendPartialMessage(data.role, data.content, data.isPartial);
            } else {
                appendMessage(data.role, data.content);
                curBubble = document.createElement('div');
                isCurBubbleInit = false;
            }
            break;
        case 'task_complete':
            // 处理任务完成状态
            setSendButtonState(true);
            loadingRow.remove();
            break;
    }
};

// Handle SSE errors
eventSource.onerror = (err) => {
    console.error('MCP error:', err);
    // appendMessage('internal', `MCP Servers connection error: ${JSON.stringify(err.message) || err}`);
};

// ================== ON PAGE LOAD (DOMContentLoaded) ==================
//  - Fetch client info
//  - Set up everything else

// Initial connection on page load
document.addEventListener('DOMContentLoaded', async () => {
    // Start connection attempt loop
    await attemptConnection(true);
    firstMessage = false;
});

// ================== 4) MAIN CHAT LOGIC: APPEND MESSAGES & TOOL BLOCKS ==================

/**
 * appendMessage(role, content):
 *   If content is an array (potential tool blocks),
 *   handle each item separately; otherwise just show a normal bubble.
 */
function appendMessage(role, content) {
    if (Array.isArray(content)) {
        content.forEach((item) => {
            if (item.type === 'tool_use' || item.type === 'tool_result') {
                appendToolBlock(item);
            } else {
                appendSingleBubble(role, item);
            }
        });
    } else {
        appendSingleBubble(role, content);
    }
}

function appendPartialMessage(role, content, isPartial) {
    if (isPartial === true){
        if (isCurBubbleInit === false){
            const row = document.createElement('div');
            row.className = 'message-row';
            if (role === 'user') {
                row.classList.add('user-message');
            } else if (role === 'assistant') {
                row.classList.add('assistant-message');
            } else {
                row.classList.add('internal-message');
            }

            curBubble.className = `bubble ${role}`;
            curBubble.innerHTML = formatAnyContent(content);

            row.appendChild(curBubble);
            chatLog.appendChild(row);

            isCurBubbleInit = true;
        }else{
            curBubble.innerHTML = formatAnyContent(content);
        }

    }

}

/**
 * appendSingleBubble(role, content): Renders a normal user/assistant/internal bubble
 */
function appendSingleBubble(role, content) {
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }
    const row = document.createElement('div');
    row.className = 'message-row';

    if (role === 'user') {
        row.classList.add('user-message');
    } else if (role === 'assistant') {
        row.classList.add('assistant-message');
    } else {
        row.classList.add('internal-message');
    }

    const bubble = document.createElement('div');
    bubble.className = `bubble ${role}`;
    bubble.innerHTML = formatAnyContent(content);

    row.appendChild(bubble);
    chatLog.appendChild(row);
    chatLog.scrollTop = chatLog.scrollHeight;
    // 添加输入中的状态
    fetch('/sec-opt-agent/task/status')
        .then(response => response.json())
        .then(data => {
            // 如果任务进行中，添加输入中的状态, 否则setSendButtonState(true))
            if (data.taskInProgress) {
                chatLog.appendChild(loadingRow);
            } else {
                setSendButtonState(true);
            }
        });

}

/**
 * appendToolBlock(item): Renders a separate row for tool_use/tool_result
 */
function appendToolBlock(item) {
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }

    const row = document.createElement('div');
    row.className = 'message-row tool-row';

    const container = document.createElement('div');
    container.className = 'tool-block';

    if (item.type === 'tool_use') {
        container.innerHTML = `
<details>
  <summary>Tool use: <strong>${item.name}</strong></summary>
  ${formatAnyContent(item.input)}
</details>`;
    } else if (item.type === 'tool_result') {
        const summary = item.is_error ? 'Tool result (Error)' : 'Tool result';
        container.innerHTML = `
<details>
  <summary>${summary}</summary>
  ${formatAnyContent(item.content)}
</details>`;
    }

    row.appendChild(container);
    chatLog.appendChild(row);
    chatLog.scrollTop = chatLog.scrollHeight;
    // 添加输入状态
    chatLog.appendChild(loadingRow);
}

// ================== UTILITY FOR FORMATTING CONTENT (JSON, MD, ETC.) ==================
function formatAnyContent(content) {
    if (typeof content === 'string') {
        // Try JSON parse
        try {
            const obj = JSON.parse(content);
            return `<pre>${escapeHTML(JSON.stringify(obj, null, 2))}</pre>`;
        } catch {
            // fallback to markdown
            return formatMarkdown(content);
        }
    }

    if (content && typeof content === 'object') {
        // plain object → JSON
        return `<pre>${escapeHTML(JSON.stringify(content, null, 2))}</pre>`;
    }

    // fallback
    return String(content);
}

/** A naive Markdown transform */
function formatMarkdown(text) {
    let safe = escapeHTML(text);
    // code fences
    safe = safe.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
    // inline code
    safe = safe.replace(/`([^`]+)`/g, '<code>$1</code>');
    // bold, italics, links, newlines
    safe = safe.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    safe = safe.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    safe = safe.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
    safe = safe.replace(/\n/g, '<br>');
    return safe;
}

/** HTML escaper for <pre> blocks, etc. */
function escapeHTML(str) {
    if (typeof str !== 'string') return String(str);
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
}

// ================== SENDING A USER QUERY (POST /message) ==================
function setSendButtonState(enabled) {
    sendBtn.disabled = !enabled;
    sendBtn.style.opacity = enabled ? '1' : '0.5';
    sendBtn.style.cursor = enabled ? 'pointer' : 'not-allowed';
}

async function sendQuery(query) {
    if (!query.trim()) return;

    try {
        const resp = await fetch('/sec-opt-agent/message', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query }),
        });
        const data = await resp.json();
        if (data.error) {
            appendMessage('internal', `Server error: ${data.error}`);
        }
    } catch (err) {
        appendMessage('internal', `Network error: ${err.message}`);
    }
}

// ================== CLEAR CONVERSATION LOG (POST /conversation/reset) ==================
clearBtn.addEventListener('click', async () => {
    try {
        const resp = await fetch('/sec-opt-agent/conversation/reset', { method: 'POST' });
        if (resp.ok) {
            chatLog.innerHTML = '';
        }
    } catch (err) {
        console.error('Error resetting conversation:', err);
    }
});

// ================== SERVER CONNECTIVITY CHECKS & RECONNECT LOGIC ==================

async function attemptConnection(isInitial = false) {
    if (isInitial) {
        if (connectionAttempts >= maxAttempts) {
            updateMcpServerStatus(STATUS.FAILED);
            appendMessage('internal', `${STATUS.FAILED_TIMEOUT}. Please try reconnecting manually.`);
            return;
        }
        connectionAttempts++;
        updateMcpServerStatus(STATUS.CONNECTING);
        // Add attempt counter inline with smaller font
        const attemptText = document.createElement('small');
        attemptText.style.cssText = `
            margin-left: 0.25rem;
            opacity: 0.7;
            font-size: 0.8em;
            white-space: nowrap;
            display: inline-block;
        `;
        attemptText.textContent = `(${connectionAttempts}/${maxAttempts})`;
        mcpServerStatus.firstElementChild.appendChild(attemptText);
    }

    try {
        const resp = await fetch('/sec-opt-agent/pingMcpServer');
        const data = await resp.json();

        if (data.status === true || data.status === 'OK') {
            updateMcpServerStatus(STATUS.CONNECTED);
            if (isInitial) {
                appendMessage('internal', 'Successfully connected to MCP server!');
                // startRegularChecks(); // 定期检查连接
            }
        } else {
            updateMcpServerStatus(STATUS.CONNECTING);
            await fetch('/sec-opt-agent/reconnect', { method: 'POST' });
        }
    } catch (err) {
        console.error('Connection attempt failed:', err);
        updateMcpServerStatus(STATUS.FAILED);
    }
}

function updateMcpServerStatus(status) {
    const isOk = status === true || status === 'OK' || status === STATUS.CONNECTED;
    if (isOk) {
        statusIcon.style.backgroundColor = '#22c55e'; // green-500
        mcpServerStatus.innerHTML = STATUS.CONNECTED;
    } else if (status === STATUS.CONNECTING) {
        statusIcon.style.backgroundColor = '#f97316'; // orange-500
        mcpServerStatus.innerHTML = `
            <div style="display: flex; align-items: center; white-space: nowrap;">
                ${STATUS.CONNECTING}
                <div class="typing-indicator" style="margin-left: 0.25rem;">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
    } else {
        statusIcon.style.backgroundColor = '#ef4444'; // red-500
        mcpServerStatus.innerHTML = status;
    }
}

// Manual reconnect button
reconnectBtn.addEventListener('click', async () => {
    connectionAttempts = 0;
    await attemptConnection(true);
});


// ================== SEND BUTTON, ENTER KEY HANDLER ==================
sendBtn.addEventListener('click', () => {
    const query = queryInput.value.trim();
    if (query) {
        sendQuery(query);
        queryInput.value = '';
    }
});

queryInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        sendBtn.click();
    }
});
