<!DOCTYPE html>
<html lang="">
<head>
    <meta charset="utf-8" />
    <title>AI助手</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="AI Chat Interface" />
    <style>
        :root {
            --primary-color: #2563eb;
            --bg-color: #f8fafc;
            --chat-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --user-message-bg: #eff6ff;
            --assistant-message-bg: #ffffff;
            --internal-message-bg: #f1f5f9;
            --tool-block-bg: #fef3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
        }

        #chatContainer {
            max-width: 1000px;
            margin: 1rem auto;
            padding: 0 1rem;
            height: calc(100vh - 2rem);
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 0.5rem;
        }

        .header h2 {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .header details {
            margin-bottom: 0.5rem;
        }

        details {
            background: var(--chat-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
        }

        details + details {
            margin-top: -0.25rem; /* Reduce space between consecutive details */
        }

        summary {
            cursor: pointer;
            font-weight: 500;
            color: var(--text-primary);
        }

        #chatLog {
            flex: 1;
            height: 0;
            background: var(--chat-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-y: auto;
            margin-bottom: 0.75rem;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        }

        .message-row {
            margin: 0.5rem 0;
            display: flex;
            gap: 0.75rem;
        }

        .message-row.user-message {
            justify-content: flex-end;
        }

        .bubble {
            max-width: 85%;
            padding: 0.6rem 0.8rem;
            font-size: 0.875rem;
            border-radius: 1.2rem;
        }

        .bubble.user {
            background: var(--user-message-bg);
            border: 1px solid #bfdbfe;
            border-bottom-right-radius: 0.3rem;
            max-width: 60%;
        }

        .bubble.assistant {
            background: var(--assistant-message-bg);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 0.3rem;
        }

        .bubble.internal {
            background: var(--internal-message-bg);
            font-size: 0.875rem;
            color: var(--text-secondary);
            max-width: 100%;
            text-align: center;
        }

        .tool-block {
            background: var(--tool-block-bg);
            border-radius: 0.8rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-size: 0.875rem;
            max-width: 100%;
            width: 100%;
        }

        .tool-row {
            margin: 0.5rem 0;
            display: flex;
            width: 100%;
        }

        .tool-block details {
            background: transparent;
            border: none;
            padding: 0;
            margin: 0;
            width: 100%;
        }

        .tool-block summary {
            padding: 0.2rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .tool-block pre {
            margin-top: 0.5rem;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.5);
            padding: 0.5rem;
        }

        #inputRow {
            gap: 0.5rem;
            margin-top: 0.75rem;
            margin-bottom: 0.5rem;
            display: grid;
            grid-template-columns: 1fr auto;
            align-items: center;
            position: sticky;
            bottom: 0;
            background: var(--bg-color);
        }

        #queryInput {
            width: 100%;
            padding: 0.5rem 0.8rem;
            font-size: 0.9rem;
            min-height: 45px;
            height: 45px;
            max-height: 150px;
            border-radius: 0.8rem;
            resize: none;
            overflow-y: auto;
            line-height: 1.5;
            vertical-align: bottom;
        }

        #queryInput:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        #sendBtn {
            height: 45px;
            padding: 0 1.5rem;
            white-space: nowrap;
            border-radius: 0.8rem;
            align-self: stretch;
        }

        button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            background: var(--primary-color);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease;
            font-size: 0.9rem;
        }

        button:hover {
            background-color: #1d4ed8;
        }

        #clearBtn, #reconnectBtn {
            background: white;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        #clearBtn:hover, #reconnectBtn:hover {
            background: #f8fafc;
        }

        .status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
        }

        #statusIcon {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        #spinner {
            display: none;
            margin: 0.5rem 0;
            padding: 1rem;
            text-align: center;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background-color: var(--text-secondary);
            border-radius: 50%;
            opacity: 0.4;
            animation: typingAnimation 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-3px);
                opacity: 0.8;
            }
        }

        .message-row.loading {
            margin-top: 0.5rem;
        }

        .bubble.loading {
            background: var(--assistant-message-bg);
            padding: 0.4rem 0.8rem;
            border: 1px solid var(--border-color);
        }

        /* Code blocks styling */
        pre {
            background: #f8fafc;
            border-radius: 0.375rem;
            padding: 0.75rem;
            overflow-x: auto;
            font-size: 0.8rem;
        }

        code {
            font-family: ui-monospace, monospace;
            font-size: 0.8rem;
        }

        /* Mobile responsiveness */
        @media (max-width: 640px) {
            #chatContainer {
                height: calc(100vh - 1rem);
                margin: 0.5rem auto;
            }

            .bubble {
                max-width: 85%;
            }

            .bubble.user {
                max-width: 75%;
                font-size: 0.85rem;
            }

            #inputRow {
                gap: 0.5rem;
            }

            #sendBtn {
                padding: 0 1rem;
            }

            .tool-block {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
<div id="chatContainer">
    <div class="header">
        <h2>AI 助手</h2>
    </div>

    <div class="status-bar">
        <div style="display: flex; align-items: center;">
            <span id="statusIcon"></span>
            <span id="mcpServerStatus">Unknown</span>
            <button id="reconnectBtn" style="margin-left: 1rem;">Reconnect</button>
        </div>
        <button id="clearBtn">Clear Chat</button>
    </div>

    <div id="chatLog"></div>

    <div id="inputRow">
        <textarea
            id="queryInput"
            rows="3"
            placeholder='研判并处置弱点:W-250519-00014/研判并处置风险:E-250523-00004'
        ></textarea>
        <button id="sendBtn">Send</button>
    </div>
</div>
<script src="client.js"></script>
<script>
    // Updated auto-resize textarea
    document.getElementById('queryInput').addEventListener('input', function() {
        // Reset height to auto first to get the correct scrollHeight
        this.style.height = 'auto';
        // Set new height based on content
        const newHeight = Math.min(this.scrollHeight, 150); // Cap at max-height
        this.style.height = newHeight + 'px';
    });
</script>
</body>
</html>
