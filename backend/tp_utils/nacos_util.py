import os

if os.environ.get("PYTHON_ENV") == "dev":
    import config_dev as config
else:
    import config

import base64
import json
import re

from Crypto.Cipher import AES
from nacos import NacosClient
from log_util import LOGGER


class NacosUtil:
    def __init__(self, server_addr=config.NACOS_ADDR):
        self.client = NacosClient(server_addresses=server_addr,logDir="/Users/<USER>/Desktop/qz/sec-opt-agent") # todo 删除logDir
        self.logger = LOGGER.get_logger(__name__)

    def get_api_featurelabel(self) -> dict:
        try:
            featurelabels = self.client.get_config('common.featurelabel.json', 'common')
            featurelabels = json.loads(featurelabels)
        except:
            self.logger.error("get_api_featurelabel error")
            return {}
        label_map = {}
        for label in featurelabels:
            if not label['delFlag'] and label['enable']:
                label_map[label['name']] = label['id']

        return label_map

    def get_mongo_pwd(self) -> str:
        encrypted_pwd = ""
        config_str = self.client.get_config('common.server.properties', 'common')
        for i in re.finditer(r"qz\.mongodb\.password\=@enc\[(.*)\]", config_str):
            encrypted_pwd = i.groups()[0]
            break

        self.logger.debug(f"mongo encrypted_pwd: {encrypted_pwd}")
        if not encrypted_pwd:
            self.logger.error("mongodb password not found")
            raise Exception("mongodb password not found")

        plain_pwd = decryptAES(encrypted_pwd)
        return plain_pwd

    def get_ck_pwd(self) -> str:
        encrypted_pwd = ""
        config_str = self.client.get_config('common.server.properties', 'common')
        for i in re.finditer(r"qz.ck.password\=@enc\[(.*)\]", config_str):
            encrypted_pwd = i.groups()[0]
            break

        self.logger.debug(f"ck encrypted_pwd: {encrypted_pwd}")
        if not encrypted_pwd:
            self.logger.error("ck password not found")
            raise Exception("ck password not found")

        plain_pwd = decryptAES(encrypted_pwd)
        return plain_pwd

    def get_kafka_pwd(self):
        pwd = ""
        config_str = self.client.get_config('common.server.properties', 'common')
        for i in re.finditer(
            r"qz.kafka.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username='audit' password\='(.*)'",
            config_str):
            pwd = i.groups()[0]
            break

        self.logger.debug(f"kafka pwd: {pwd}")
        if not pwd:
            self.logger.error("kafka password is null")
            raise Exception("kafka password is null")

        return pwd

    def get_weakness_desc(self):
        try:
            weakness_rules = self.client.get_config('discover.weakness.rules.json', 'discover')
            weakness_rules = json.loads(weakness_rules)
        except:
            self.logger.error("get_weakness_desc error")
            return {}

        weakness_map = {}
        for rule in weakness_rules:
            if not rule['delFlag']:
                weakness_map[rule['name']] = rule['description']
        return weakness_map

    def get_datalabel_map(self) -> dict:
        try:
            datalabels = self.client.get_config('common.datalabel.json', 'common')
            datalabels = json.loads(datalabels)
        except:
            self.logger.error("get_datalabel_map error")
            return {}

        datalabel_map = {}
        for datalabel in datalabels:
            datalabel_map[datalabel['id']] = datalabel['name']
        return datalabel_map


NACOS_UTIL = NacosUtil()


def decryptAES(ciphertext, key='_QUAN_ZHI_KE_JI_') -> str:
    # 确保 key 是 16 字节（AES-128）
    key_bytes = key.encode('utf-8').ljust(16, b'\0')[:16]

    # 解密器（ECB 模式）
    cipher = AES.new(key_bytes, AES.MODE_ECB)

    # Base64 解码
    encrypted_bytes = base64.b64decode(ciphertext)

    # 执行解密
    decrypted_bytes = cipher.decrypt(encrypted_bytes)

    # 去除填充（假设使用的是 PKCS7 padding）
    pad_len = decrypted_bytes[-1]
    decrypted = decrypted_bytes[:-pad_len].decode('utf-8')

    return decrypted


if __name__ == '__main__':
    print(NacosUtil("*************:8848").get_mongo_pwd())
    # print(NACOS_UTIL.get_api_featurelabel())
    # print(NACOS_UTIL.get_ck_pwd())
    # print(NACOS_UTIL.get_kafka_pwd())
    # print(NACOS_UTIL.get_weakness_desc())
    # print(NACOS_UTIL.get_datalabel_map())
    # print(decryptAES("GblzLvQTqxTgeq9wQ982yarDb4t0pD8lJPEX6rONhxk="))
