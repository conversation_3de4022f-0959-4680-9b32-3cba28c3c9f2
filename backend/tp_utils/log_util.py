import logging
import os
from logging.handlers import RotatingFileHandler

logNameToLevel = {
    'CRITICAL': logging.CRITICAL,
    'FATAL': logging.FATAL,
    'ERROR': logging.ERROR,
    'WARN': logging.WARNING,
    'WARNING': logging.WARNING,
    'INFO': logging.INFO,
    'DEBUG': logging.DEBUG,
    'NOTSET': logging.NOTSET,
}


class TLogger:
    def __init__(self, log_path: str = "../logs", log_filename: str = "tp.log", level: str = "DEBUG"):
        self.logfile = os.path.join(log_path, log_filename)
        self.level = logNameToLevel.get(level, logging.DEBUG)
        os.makedirs(log_path, exist_ok=True)

    def get_logger(self, name='T'):
        self.LOGGER = logging.getLogger(name)
        if self.LOGGER.hasHandlers():
            return self.LOGGER
        _log_handler = logging.StreamHandler()
        _fmt = '[%(asctime)s](%(levelname)s)%(filename)s:%(lineno)d %(name)s: %(message)s'
        _formatter = logging.Formatter(fmt=_fmt)
        _log_handler.setFormatter(_formatter)
        _log_handler.setLevel(self.level)
        self.LOGGER.addHandler(_log_handler)

        _file_handler = RotatingFileHandler(
            filename=self.logfile,
            maxBytes=1024 * 1024 * 5,
            backupCount=10,
            encoding='utf8'
        )

        _file_formatter = logging.Formatter(_fmt)
        _file_handler.setFormatter(_file_formatter)
        _file_handler.setLevel(self.level)
        self.LOGGER.addHandler(_file_handler)
        self.LOGGER.propagate = False
        self.LOGGER.setLevel(self.level)
        return self.LOGGER


TLOGGER = TLogger()
