import clickhouse_connect

from nacos_util import NACOS_UTIL, config


class ClickhouseUtil:
    def __init__(self, host: str = config.CK_HOST, port: int = config.CK_PORT, username: str = "audit", password: str = None,
                 database: str = "audit"):
        self.client = clickhouse_connect.get_client(
            host=host,
            port=port,
            username=username,
            password=password,
            database=database
        )


CK_UTIL = ClickhouseUtil(password=NACOS_UTIL.get_ck_pwd())
