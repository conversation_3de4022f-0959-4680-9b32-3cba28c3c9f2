/**
 * # Chatbot Server with Real-Time Tool Execution
 *
 * Server for a chatbot integrated with Apify Actors and an MCP client.
 * Processes user queries, invokes tools dynamically, and streams real-time updates using Server-Sent Events (SSE)
 *
 * Environment variables:
 * - `APIFY_TOKEN` - API token for Apify (when using actors-mcp-server)
 */

import path from 'path';
import { fileURLToPath } from 'url';

import cors from 'cors';
import dotenv from 'dotenv';
import express from 'express';

import { McpHost } from './core/mcpHost.js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);
dotenv.config({ path: path.resolve(dirname, '../.env') });
const HOST = 'http://localhost';
const PORT = '3000';
const app = express();
app.use(express.json());
app.use(cors());

// Serve your public folder (where index.html is located)
const publicPath = path.join(path.dirname(filename), 'public');
const publicUrl = `${HOST}:${PORT}`;
app.use("/sec-opt-agent", express.static(publicPath));

// 4) We'll store the SSE clients (browsers) in an array
type SSEClient = { id: number; res: express.Response };
let sseClients: SSEClient[] = [];
let clientIdCounter = 0;

const client = new McpHost();

// 添加历史消息管理
const firstMessage = { role: 'assistant', content: "你好，我是大全小知，我可以帮你运营产品上的弱点和风险，你可以这样对我说：研判并处置弱点:W-250319-00014 或者 研判并处置风险:E-250323-00004"  };
let chatMessages: { role: string; content: string }[] = [firstMessage];

// 添加任务状态管理
let taskInProgress = false;


// 5) SSE endpoint for the client.js (browser) to connect to
app.get('/sec-opt-agent/sse', (req, res) => {
    // Required headers for SSE
    res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
    });
    res.flushHeaders();

    const clientId = ++clientIdCounter;
    sseClients.push({ id: clientId, res });

    // 发送当前状态给新连接的客户端
    const initialState = {
        type: 'status',
        taskInProgress,
        messages: chatMessages
    };
    res.write(`data: ${JSON.stringify(initialState)}\n\n`);

    // If client closes connection, remove from array
    req.on('close', () => {
        sseClients = sseClients.filter((c) => c.id !== clientId);
    });
});


// 获取任务状态
app.get('/sec-opt-agent/task/status', (_req, res) => {
    res.json({ taskInProgress });
});

// 更新任务状态为完成
app.post('/sec-opt-agent/task/complete', (_req, res) => {
    taskInProgress = false;
    res.json({ ok: true });
});


// 修改消息处理路由
app.post('/sec-opt-agent/message', async (req, res) => {
    const { query } = req.body;
    if (!query) {
        return res.status(400).json({ error: 'Missing "query" field' });
    }
    try {
        // 开始新任务时，清空历史消息
        chatMessages = [firstMessage];
        taskInProgress = true;

        // 广播任务开始状态
        broadcastSSE({ type: 'status', taskInProgress: true });

        // 添加用户消息到历史
        const userMessage = { role: 'user', content: query };
        chatMessages.push(userMessage);
        broadcastSSE({ type: 'message', ...userMessage });

        await client.processUserQuery(query, (type: 'message' | 'status' | 'task_complete', role: string, content: string, isPartial?: boolean) => {
            switch (type) {
                case 'message':
                    if (!isPartial) {
                        // 非部分消息才保存到历史
                        chatMessages.push({ role, content });
                    }

                    broadcastSSE({
                        type: 'message',
                        role,
                        content,
                        isPartial
                    });
                    break;
                case 'status':
                    break;
                case 'task_complete':
                    if (content === "true") {
                        broadcastSSE({ type: 'task_complete', taskInProgress: false });
                    }
                    break;
            }
        });

        // 任务完成时更新状态
        taskInProgress = false;
        broadcastSSE({ type: 'task_complete', taskInProgress: false });

        return res.json({ ok: true });
    } catch (err) {
        taskInProgress = false;
        broadcastSSE({ type: 'status', taskInProgress: false });
        return res.json({ error: (err as Error).message });
    }
});

/**
 * Periodically check if the main server is still reachable.
 */
app.get('/sec-opt-agent/pingMcpServer', async (_req, res) => {
    try {
        // Attempt to ping the main MCP server
        const response = await client?.mcp?.isConnected();
        res.json({ status: response });
    } catch (err) {
        res.json({ status: 'Not connected', error: (err as Error).message });
    }
});

app.post('/sec-opt-agent/reconnect', async (_req, res) => {
    try {
        console.log('Reconnecting to main server');
        await client.mcp?.connectToServer();
        const response = await client?.mcp?.isConnected();
        res.json({ status: response });
    } catch (err) {
        console.error(`Error reconnecting to main server: ${err}`);
        res.json({ status: 'Not connected', error: (err as Error).message });
    }
});

// 修改重置对话路由
app.post('/sec-opt-agent/conversation/reset', async (_req, res) => {
    try {
        taskInProgress = false;
        chatMessages = [firstMessage];

        // 广播重置状态
        broadcastSSE({ type: 'status', taskInProgress: false });

        broadcastSSE({ type: 'message', ...firstMessage });

        res.json({ ok: true });
    } catch (err) {
        res.json({ error: (err as Error).message });
    }
});

/**
 * Broadcasts an event to all connected SSE clients
 */
// 定义 SSE 消息结构
interface SSEMessage {
    type: 'message' | 'status' | 'task_complete';
    role?: string;
    content?: string;
    isPartial?: boolean;
    taskInProgress?: boolean;
}
function broadcastSSE(data: SSEMessage) {
    for (const c of sseClients) {
        c.res.write(`data: ${JSON.stringify(data)}\n\n`);
    }
}

app.get('/sec-opt-agent/*', (_req, res) => {
    res.sendFile(path.join(publicPath, 'index.html'));
});

app.listen(PORT, async () => {
    await client.init();
    await client?.mcp?.connectToServer();
    console.info(`Serving from path ${path.join(publicPath, 'index.html')}`);
    const msg = `Navigate to ${publicUrl} to interact with chat-ui interface.`;
    console.info(msg);
});
