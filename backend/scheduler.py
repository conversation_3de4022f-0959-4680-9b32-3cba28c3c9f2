import os
from log_util import LOGGER

logger = LOGGER.get_logger(os.path.basename(__file__))

import importlib
import time
import os
import traceback

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED


class Scheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.scheduler.add_listener(self.event_listener, EVENT_JOB_ERROR | EVENT_JOB_EXECUTED)

    @staticmethod
    def event_listener(event):
        """
        APScheduler 事件监听器。
        当任务发生错误或成功执行时，会触发此函数。
        """
        if event.exception:
            # 任务执行失败
            # print(f"\n{'='*30}")
            # print(f"任务 '{event.job_id}' 发生错误！")
            # print(f"错误类型: {type(event.exception).__name__}")
            # print(f"错误信息: {event.exception}")
            # print(f"详细回溯:\n{event.traceback}") # 完整的堆栈跟踪
            # print(f"发生时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            # print(f"{'='*30}\n")
            # 在这里你可以添加自定义的错误处理逻辑，例如：
            # - 发送邮件通知管理员
            # - 将错误信息记录到数据库
            # - 重试任务 (如果逻辑允许)
            # - 调用其他监控系统接口
            logger.error(f"Job '{event.job_id}' failed: {event.exception}\n{event.traceback}")
        else:
            # 任务成功执行 (如果任务有返回值，event.retval 就是返回值)
            logger.info(f"Job '{event.job_id}' executed successfully.")
        # 如果你想监听所有事件，可以不加条件判断，直接打印 event 对象
        # print(f"Event: {event}")

    def run(self):
        self.load_tasks()
        self.scheduler.start()

        logger.info('Press Ctrl+{0} to exit'.format('Break' if os.name == 'nt' else 'C'))
        try:
            # This is here to simulate application activity (which keeps the main thread alive).
            while True:
                time.sleep(30)
                self.load_tasks()
        except (KeyboardInterrupt, SystemExit):
            # Not strictly necessary if daemonic mode is enabled but should be done if possible
            self.scheduler.shutdown()

    def load_tasks(self):
        task_path = os.path.join(os.path.dirname(__file__), "tasks")

        for root, dirs, files in os.walk(task_path):
            for file_name in files:
                if not file_name.endswith("_task.py"):
                    continue
                file_name = file_name.split('.')[0]

                # 如果任务已经存在，跳过
                if self.scheduler.get_job(file_name):
                    continue

                moudule_name = 'tasks' + root.replace(task_path, '').replace(os.sep, '.') + '.' + file_name
                try:
                    task = importlib.import_module(moudule_name)
                    if "INTERVAL" not in task.__dict__:
                        task.INTERVAL = 60 * 60 * 24
                    self.scheduler.add_job(task.run, 'interval', seconds=task.INTERVAL, id=file_name)
                except Exception as e:
                    logger.error(traceback.print_exc())
                    continue


if __name__ == '__main__':
    Scheduler().run()
