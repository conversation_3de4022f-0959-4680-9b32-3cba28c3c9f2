{"mcpServers": {"api-weakness": {"command": "uv", "args": ["--directory", "/Users/<USER>/Desktop/qz/sec-opt-agent/mcp-servers/api-weakness", "run", "main.py"], "type": "stdio", "env": {}, "timeout": 60, "disabled": true}, "api-risk": {"command": "uv", "args": ["--directory", "/Users/<USER>/Desktop/qz/sec-opt-agent/mcp-servers/api-risk", "run", "main.py"], "type": "stdio", "env": {}, "timeout": 60, "disabled": true}}}