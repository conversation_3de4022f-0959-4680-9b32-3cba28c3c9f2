import requests
from tp_utils.log_util import LOGGER

logger = LOGGER.get_logger("api_risk_mcp")
from mcp.server.fastmcp import FastMCP
from pydantic import Field

from mongo_util import MONGO_UTIL
from clickhouse_util import CK_UTIL
from nacos_util import NACOS_UTIL, config

# Initialize FastMCP server
mcp = FastMCP("api-risk")


# ------- 风险处理工具⬇ -----------
@mcp.tool(description="通过风险ID修改风险的状态")
async def handle_risk_from_id(
    risk_id: str = Field(description="风险ID"),
    state_name: str = Field(description="处理状态，状态值从`已确认`和`已忽略`中选一个"),
    suggest: str = Field(description="处理依据，这个风险为什么确认或者为什么忽略的理由")
) -> str:
    # 待确认：0 已忽略：1 已确认：2
    state = 0
    if "确认" in state_name:
        state = 2
    elif "忽略" in state_name:
        state = 1
    else:
        raise ValueError("state argument error")

    # 改为调用后端接口
    old_rgg_risk_info = MONGO_UTIL.db.get_collection('aggRiskInfo').find_one(
        {"operationId": risk_id})  # 待确认：0 已忽略：1 已确认：2
    if not old_rgg_risk_info:
        raise ValueError(f"未找到风险。")
    data = {
        "type": "AGG_RISK_INFO",
        "aggRiskInfoAiInfo": {
            "riskOptReason": suggest,
            "state": state,
            "id": old_rgg_risk_info['_id']
        }
    }
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "OPEN_API"
    }
    rsp = requests.post(f"http://{config.BACKEND_ADDR}/audit-apiv2/api/llmConfig/receiveAiStudyResult", json=data,
                        headers=headers)

    logger.debug(f"调用参数:{data},接口响应: {rsp.text}")
    if rsp.status_code != 200:
        logger.error(f"风险处理失败: {rsp.text}")
        return "风险处理失败"

    # MONGO_UTIL.db.get_collection('aggRiskInfo').update_one({"operationId": risk_id},
    #                                                        {
    #                                                            "$set": {
    #                                                                "state": state,
    #                                                                "stateName": state_name,
    #                                                                "aiInfo": {"riskOptReason": suggest}
    #                                                            }
    #                                                        })
    # # 更新风险关联的异常告警状态
    # now = int(datetime.datetime.now().timestamp() * 1000)
    # agg_risk_info = MONGO_UTIL.db.get_collection('aggRiskInfo').find_one(
    #     {"operationId": risk_id}, {"_id": 1, "entities": 1, 'name': 1, 'level': 1})  # 待确认：0 已忽略：1 已确认：2
    # if not agg_risk_info:
    #     raise ValueError(f"未找到风险。")
    #
    # MONGO_UTIL.db.get_collection('riskInfo').update_many({"aggRiskId": agg_risk_info['_id']},
    #                                                      {"$set": {
    #                                                          "state": state,
    #                                                          "operateName": "AI",
    #                                                          "operateTime": now
    #                                                      }})
    #
    # # 更新操作日志
    # MONGO_UTIL.db.get_collection('aggRiskOperationLog').insert_one({
    #     "riskId": agg_risk_info['_id'],
    #     "operateName": "AI",
    #     "oldState": 0,
    #     "newState": state,
    #     "operateTime": now,
    # })
    #
    # # 更新ipInfo.riskInfo
    # """"
    # "riskInfo": {
    #     "riskNames": [ ],
    #     "levelNumMap": { },
    #     "riskLevelLow": Long("0"),
    #     "riskLevelMedium": Long("0"),
    #     "riskLevelHigh": Long("0"),
    #     "levelName": "无风险",
    #     "riskLevel": Int32("0"),
    #     "riskFirstTime": Long("0"),
    #     "riskCnt": Long("0"),
    #     "totalRiskCnt": Long("0"),
    #     "confirmRiskCount": Long("0")
    # },
    # """
    # entities = agg_risk_info['entities']
    # for entitie in entities:
    #     if entitie['type'] == "IP":
    #         risk_ip = entitie['value']
    #         old_ip_info = MONGO_UTIL.db.get_collection('ipInfo').find_one({'ip': risk_ip}, {'riskInfo': 1,'riskLevel':1,'riskLevelName':1})
    #         if old_ip_info:
    #             old_risk_info = old_ip_info['riskInfo']
    #
    #             if state == 2:  # 已确认
    #                 old_risk_info["confirmRiskCount"] = old_risk_info['confirmRiskCount'] + 1
    #
    #                 MONGO_UTIL.db.get_collection('ipInfo').update_one({'_id': old_ip_info['_id']},
    #                                                                   {'$set': {"riskInfo": old_risk_info}})
    #
    #             elif state == 1:  # 已忽略
    #                 levelNumMap = old_risk_info['levelNumMap']
    #                 riskLevelLow = old_risk_info['riskLevelLow']
    #                 riskLevelMedium = old_risk_info['riskLevelMedium']
    #                 riskLevelHigh = old_risk_info['riskLevelHigh']
    #                 if agg_risk_info['level'] == 1:
    #                     riskLevelLow = old_risk_info['riskLevelLow'] - 1
    #                     levelNumMap.update({"1": riskLevelLow})
    #                 elif agg_risk_info['level'] == 2:
    #                     riskLevelMedium = old_risk_info['riskLevelMedium'] - 1
    #                     levelNumMap.update({"2": riskLevelMedium})
    #                 elif agg_risk_info['level'] == 3:
    #                     riskLevelHigh = old_risk_info['riskLevelHigh'] - 1
    #                     levelNumMap.update({"3": riskLevelHigh})
    #                 else:
    #                     pass
    #
    #                 if riskLevelHigh > 0:
    #                     levelName = "高风险"
    #                     riskLevel = 3
    #                 elif riskLevelMedium > 0:
    #                     levelName = "中风险"
    #                     riskLevel = 2
    #                 elif riskLevelLow > 0:
    #                     levelName = "低风险"
    #                     riskLevel = 1
    #                 else:
    #                     levelName = "无风险"
    #                     riskLevel = 0
    #
    #                 riskNames = old_risk_info['riskNames']
    #                 if agg_risk_info['name'] in riskNames:
    #                     riskNames.remove(agg_risk_info['name'])
    #                 riskInfo = {
    #                     "riskNames": riskNames,
    #                     "levelNumMap": levelNumMap,
    #                     "riskLevelLow": riskLevelLow,
    #                     "riskLevelMedium": riskLevelMedium,
    #                     "riskLevelHigh": riskLevelHigh,
    #                     "levelName": levelName,
    #                     "riskLevel": riskLevel,
    #                     "totalRiskCnt": old_risk_info['totalRiskCnt'] - 1,
    #                     "confirmRiskCount": old_risk_info['confirmRiskCount']
    #                 }
    #                 MONGO_UTIL.db.get_collection('ipInfo').update_one({'_id': old_ip_info['_id']},
    #                                                                   {'$set': {"riskInfo": riskInfo,
    #                                                                             "riskLevel": riskLevel,
    #                                                                             "riskLevelName": levelName}})

    return "风险处理成功"


@mcp.tool(description="通过风险ID查询风险信息")
async def get_risk_from_id(
    risk_id: str = Field(description="风险ID"),
) -> str:
    agg_risk_info = MONGO_UTIL.db.get_collection('aggRiskInfo').find_one(
        {"operationId": risk_id, "state": 0})  # 待确认：0 已忽略：1 已确认：2
    if not agg_risk_info:
        raise ValueError(f"未找到风险。")

    return assemble_risk_info(agg_risk_info['policySnapshot']['name'], agg_risk_info)


@mcp.tool(description="查询指定IP的所有风险")
async def get_all_risk_from_ip(
    risk_ip: str = Field(description="风险IP"),
) -> str:
    all_result = []
    # 待确认：0 已忽略：1 已确认：2
    count = 0
    for agg_risk_info in MONGO_UTIL.db.get_collection('aggRiskInfo').find({"ip": risk_ip}):
        try:
            risk_info = assemble_risk_info(agg_risk_info['policySnapshot']['name'], agg_risk_info)
        except:
            continue
        count += 1
        risk_info += f"<风险状态>{agg_risk_info['stateName']}</风险状态>"
        all_result.append(f"<风险{count}>{risk_info}</风险{count}>")

    if all_result:
        return f"此IP涉及的所有风险如下：\n<风险清单>{"\n".join(all_result)}</风险清单>"
    else:
        return f"未找到此IP相关的风险。"


def assemble_risk_info(risk_name, agg_risk_info) -> str:
    abnormal_info = MONGO_UTIL.db.get_collection('riskInfo').find_one({"aggRiskId": agg_risk_info['_id']}, {"desc": 1})
    abnormal_desc = abnormal_info.get("desc", "")

    # 查询风险样例
    risk_sample = MONGO_UTIL.db.get_collection('riskSample').find_one({"aggRiskId": agg_risk_info['_id']})
    if not risk_sample:
        raise ValueError("无风险样例！")

    risk_sample['httpEvent']['req']['body'] = risk_sample['httpEvent']['req']['body'][:1000]
    risk_sample['httpEvent']['rsp']['body'] = risk_sample['httpEvent']['rsp']['body'][:1000]
    result = (
        f"<风险名称>{risk_name}</风险名称>"
        f"<风险id>{agg_risk_info['operationId']}</风险id>"
        f"<风险HTTP日志样例>\n<http请求>{risk_sample['httpEvent']['req']}</http请求> \n<http响应>{risk_sample['httpEvent']['rsp']}</http响应></风险HTTP日志样例>"
        f"<风险描述>{agg_risk_info['desc']} 其中一个异常信息为：{abnormal_desc}</风险描述>".replace("异常", "违反规则")
    )

    remark = ""  # 备注信息
    tips = ""  # 风险研判关键因素
    if risk_name == "API单日访问量超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。风险定义：比如3月1号到3月30号这30天，每天API的访问量都是100左右，基线就是100。4月1号这一天，API的访问量为10000，远远超出基线，说明这一天这个接口可能被恶意利用或被攻击。"
        tips = "由于基线的计算没有考虑过特殊情况，比如节假日或者历史峰值，可能会因此导致误报。因此需要分析API的具体业务，根据业务情况综合判断访问量过大是否具有风险。"
    elif risk_name == "API单次响应数据量超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。风险定义：比如3月1号到3月30号这30天，API每次被访问，返回的数据量都是100左右，基线就是100。4月1号这一天，这个API某次被访问，返回了10000数据量，远远超出基线，说明这个接口可能被恶意利用或被攻击，拉取了大量数据。"
        tips = "由于基线的计算没有考虑过特殊情况，比如节假日或者历史峰值，可能会因此导致误报。可以先分析API的具体业务，再根据业务情况综合判断数据量过大是否具有风险。"
    elif risk_name == "IP使用多账号":
        remark = "风险定义：一个IP在一天内使用了多个账号"
        tips = "如果这个IP是代理IP则会导致误报。"
    elif risk_name == "IP单日访问量超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。风险定义：比如3月1号到3月30号这30天，IP每天的访问量都是100左右，基线就是100。4月1号这一天，IP的访问量为10000，远远超出基线，说明这一天这个IP可能被恶意利用。"
        tips = "由于基线的计算没有考虑过特殊情况，比如节假日或者历史峰值，可能会因此导致误报。可以结合IP当日获取的数据类型、数据量综合分析是否具有风险。"
    elif risk_name == "IP执行参数遍历":
        remark = "风险定义：IP在一天内，对一个API进行了参数遍历。"
        # todo 添加可遍历参数 riskSample->httpEvent.enumerateParaInfos[0].path

        tips = "产品提取的所谓的可遍历参数可能是错误的，需要判断参数是否确实属于可遍历参数。"
    elif risk_name == "IP执行翻页遍历":
        remark = "风险定义：IP在一天内，对一个API进行了翻页遍历。比如: 通过切换翻页参数(pagesize=1,pagesize=2....)频繁访问API获取大量数据。"
        # todo 添加 多个 可遍历参数 需要吗？是不是可以从ua辅助判断？
        tips = "产品提取的所谓的翻页参数可能是错误的，需要判断参数是否确实属于翻页参数。以及翻页的值是否具备连续性。"
    elif risk_name == "IP撞库":
        remark = "风险定义：IP在一天内使用大量账号密码组合爆破登录接口，企图爆破到正确的账号密码从而进入系统获取数据。"
        tips = "判断这个接口是否是登录接口。"
    elif risk_name == "Web攻击":
        # todo riskSample.httpEvent.scanInfo.attackKeywords 和 scanInfo.scanType
        tips = "通过攻击接口样例，结合产品提取的攻击字段和攻击类型判断是否是web攻击。"
    elif risk_name == "境外IP拉取大量敏感数据":
        tips = "分析接口业务，根据业务属性做综合判断是否存在风险。"
    elif risk_name == "应用单日访问量超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。风险定义：比如3月1号到3月30号这30天，应用每天的访问量都是100左右，基线就是100。4月1号这一天，应用的访问量为10000，远远超出基线，说明这一天这个应用可能被攻击。"
        tips = "根据接口信息，猜测应用业务，综合判断是否存在风险。"
    elif risk_name == "异常请求查询敏感数据":
        remark = "风险定义：恶意用户使用技术手伪装成正常用户访问接口。比如：UA使用python,java，但是又自作聪明模拟了referer；比如浏览器UA发起的POST请求，但是无referer等等。都是恶意用户没有伪装好自己而露出的破绽。"
        tips = "分析接口的请求和响应，判断是否存在伪装行为。结合接口返回的敏感数据，综合判断风险的影响。"
    elif risk_name == "异常参数获取敏感数据":
        remark = "风险定义：请求的参数与请求投中相关联的参数(比如凭证)不一致，正常情况下这两个值是相同的。比如张三访问 http://xxxx.com/personInfo 接口，通过传入的 mobile 参数查询个人信息。请求的cookie中有phone=13888888888，并且这个接口的POST 参数 mobile=13888888888,他们是相同的，这个接口统计了200次，发现都是这样的规律，那么这两个参数会被关联起来。后续如果发现这个接口的请求中，POST 参数的mobile和 cookie中 phone的值不一致，说明该请求是异常请求。"
        # todo 获取关联参数  httpApi.relatedParamInfos[0].relatedKey
        tips = "根据API的请求和响应判断提取的关联参数是否正确，这个接口的提取参数为："
    elif risk_name == "手机号单日响应数据量超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。"
        tips = ""
    elif risk_name == "渗透测试尝试":
        tips = "分析接口样例，根据请求返回，和攻击方式和证据，判断是否存在风险。"
    elif risk_name == "登录API存在弱密码登录账号":
        tips = "这个风险的准确率很高，一般都是对的，直接确认即可。"
    elif risk_name == "短信炸弹":
        remark = "风险定义：短信发送接口在一天内对同一个手机号发送大量短信。"
        tips = "判断接口是否为短信发生接口，以及短信是否发送成功。"
    elif risk_name == "账号单日访问次数超出基线":
        remark = "基线的定义：根据最近30天的访问情况计算的阈值。风险定义：比如3月1号到3月30号这30天，账号每天的访问量都是100左右，基线就是100。4月1号这一天，账号的访问量为10000，远远超出基线，说明这一天这个账号可能被恶意利用。"
    elif risk_name == "账号异地登录":
        pass
    elif risk_name == "账号暴力破解":
        remark = "风险定义：对某一账号使用大量密码进行登录尝试"
        tips = "判断接口是否为登录接口"
    elif risk_name == "验证码暴破":
        remark = "风险定义：对手机号+验证码的登录方式登录的接口，尝试暴力破解验证码。"
        tips = "判断接口是否为手机号+验证码方式的登录接口"
    else:
        risk_policy_data = MONGO_UTIL.db.get_collection('riskPolicy').find_one({"name": risk_name})
        if risk_policy_data and risk_policy_data.get("desc"):
            remark = f"风险定义：{risk_policy_data.get("desc")}"
        else:
            raise ValueError("未找到此风险规则或此风险定义为空。")

    if remark:
        result += f"<备注>{remark}</备注>"
    if tips:
        result += f"<研判提示>{tips}</研判提示>"

    return result


# ------- 风险处理工具⬆ -----------


# ------- 日志查询工具⬇ -----------
@mcp.tool(description="查询IP的http日志")
async def get_event_from_ip(
    risk_ip: str = Field(description="风险IP"),
    date: str = Field(description="日期, 格式为2020-05-01"),
    limit: int = Field(default=10, description="限制返回的日志数量, 默认最近10条"),
) -> str:
    all_event = []
    query = f"SELECT req, rsp from future_http_defined_event WHERE ip_v4='{risk_ip}' AND date='{date}' LIMIT {limit}"
    result = CK_UTIL.client.query(query)
    count = 0
    for row in result.result_rows:
        count += 1
        all_event.append(f"<日志{count}><请求>{row[0]}</请求><响应>{row[1]}</响应></日志{count}>")

    return f"{all_event}"


@mcp.tool(description="通过event_id查询日志")
async def get_event_from_id(
    event_id: str = Field(description="事件ID"),
) -> str:
    all_event = []
    query = f"SELECT req, rsp from future_http_defined_event WHERE id='{event_id}'"
    result = CK_UTIL.client.query(query)
    for row in result.result_rows:
        all_event.append(f"<日志><请求>{row[0]}</请求><响应>{row[1]}</响应></日志>")

    return f"{all_event}"


# ------- 日志查询工具⬆ -----------

# ------- 画像查询工具⬇ -----------
@mcp.tool(description="获取IP画像")
async def get_portrait_from_ip(
    ip: str = Field(description="ip"),
) -> str:
    ipinfo = MONGO_UTIL.db.get_collection("ipInfo").find_one({"ip": ip})
    if not ipinfo:
        raise ValueError("未找到此IP！")
    datalabel_map = NACOS_UTIL.get_datalabel_map()
    ipinfo['reqDataLabelList'] = [datalabel_map.get(i, None) if datalabel_map.get(i, None) else i for i in
                                  ipinfo['reqDataLabelList']]
    ipinfo['rspDataLabelList'] = [datalabel_map.get(i, None) if datalabel_map.get(i, None) else i for i in
                                  ipinfo['rspDataLabelList']]

    return f"{ipinfo}"


#
# @mcp.tool(description="获取接口画像")
# async def get_portrait_from_url(
#     url: str = Field(description="url"),
# ) -> str:


# ------- 画像查询工具⬆ -----------

if __name__ == "__main__":
    # Initialize and run the server
    mcp.run(transport='stdio')
    # print(asyncio.run(get_all_risk_from_ip("*************")))
    # asyncio.run(handle_risk_from_id("E-250313-00020", "已确认", "skdflsdkfjlksdjf"))
