import json
import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"

from openai import OpenAI
from log_util import LOGGER
import requests
from nacos_util import NACOS_UTIL
from nacos_util import config
from mongo_util import MONGO_UTIL

logger = LOGGER.get_logger("tag_api")

INTERVAL = 60 * 10 * 1  # fixme 为了测试方便改成10分钟跑一次


class TagApi:
    def __init__(self):
        self.api_featurelabel = NACOS_UTIL.get_api_featurelabel()

        self.system_prompt = [{
            "role": "system",
            "content": f"""你是一个 API 日志分析助手，任务是：**判断一条HTTP请求/响应日志所属的业务类型**，并输出最匹配的标签及理由。

---

## 判断规则
1. 根据URL、请求头、请求体、响应状态码、响应头、响应体等维度综合分析
2. 从下列标签中选出相匹配的，标签可以同时有多个，如果无匹配则保持空数组
<tag_list>{"、".join(self.api_featurelabel.keys())}</tag_list>

---

## 输出格式
- 返回 JSON 对象，格式如下：
{{
  "aiTagBussinessReason": "用中文说明主要判断依据",
  "aiBussinessTags": ["匹配的标签（如有），否则为空数组"]
}}
"""}]

        self.mongo = MONGO_UTIL

    def _init(self):
        self.httpApi = self.mongo.db.get_collection("httpApi")
        self.httpSample = self.mongo.db.get_collection("httpSample")
        self.LLMConfig = self.mongo.db.get_collection("LLMConfig")

        # 读取LLM配置
        llm_cfg = self.LLMConfig.find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            return False
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        self.client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        return True

    def _get_uris(self, max_uris=5000):
        """
        :param max_uris: 一次只筛选max_uris个接口，防止数据太多卡死
        :return:
        """
        uris = []
        for http_api in self.httpApi.find(
            {
                "aiInfo.bussinessTags": {"$exists": False},
                "delFlag": False,
                "userDelFlag": False
            },
            no_cursor_timeout=True):
            uris.append(http_api.get("uri", ""))
            max_uris -= 1
            if max_uris <= 0:
                break

        for uri in uris:
            yield uri

    # todo 正常接口可能恰好获取到扫描流量的样例，导致误报
    def _get_sample(self, uri):
        sample = self.httpSample.find_one({"uri": uri}, {"req": 1, "rsp": 1})
        if sample:
            # 样例太长会让小模型胡言乱语，这里切割一下
            sample['rsp']['body'] = sample.get("rsp", {}).get("body", "")[:1000]
            sample['req']['body'] = sample.get("req", {}).get("body", "")[:1000]
        return sample

    def _analyze_api(self, sample: dict) -> dict:
        messages = self.system_prompt + [{
            "role": "user",
            "content": f"{sample}"
        }]
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}
        )

        message = response.choices[0].message.content

        result = {}
        try:
            result = json.loads(message)
        except:
            logger.error("message is not a json")

        return result

    def _save_tag(self, uri, result: dict):
        api_data = self.httpApi.find_one({"uri": uri}, {"aiInfo": 1, "featureLabels": 1})
        aiInfo = api_data.get("aiInfo", {})

        # 标签转换成英文id
        bussinessTags = [self.api_featurelabel.get(i) for i in result.get("aiBussinessTags", [])]
        bussinessTags = [item for item in bussinessTags if item is not None]
        buf = {
            "tagBussinessReason": result.get("aiTagBussinessReason", ""),
            "bussinessTags": bussinessTags
        }
        aiInfo.update(buf)

        featurelabels = api_data.get("featureLabels", [])
        featurelabels.extend(bussinessTags)
        featurelabels = list(set(featurelabels))
        self.httpApi.update_one({"uri": uri},
                                {"$set": {
                                    "featureLabels": featurelabels,
                                    "aiInfo": aiInfo
                                }})

    def run(self, max_uris=5000):
        if not self._init():
            logger.error("初始化失败")
            return

        uri_count = 0
        for uri in self._get_uris(max_uris):
            sample = self._get_sample(uri)
            if not sample:
                logger.debug(f"uri:{uri}未找到样例")
                continue
            if uri_count % 100 == 0:
                logger.debug(f"uri_count: {uri_count}")
            uri_count += 1
            # 请求响应发送给大模型，分析接口，打标
            result = self._analyze_api(sample)
            self._save_tag(uri, result)


def run(max_uris=3):  # fixme 为了测试方便改成一次跑3个
    t = TagApi()
    t.run(max_uris)


if __name__ == '__main__':
    run(1)
