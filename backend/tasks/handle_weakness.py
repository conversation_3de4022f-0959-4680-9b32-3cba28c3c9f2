import json
import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"

from openai import OpenAI
from log_util import LOGGER
from nacos_util import NACOS_UTIL
from mongo_util import MONGO_UTIL

logger = LOGGER.get_logger("ai_handle_weakness")

INTERVAL = 60 * 10 * 1  # fixme 为了测试方便改成10分钟跑一次


class HandleWeakness:
    def __init__(self):
        self.system_prompt = [{
            "role": "system",
            "content": f"""你是一个 API 弱点分析助手，任务是：**判断一条 HTTP 请求/响应日志是否存在弱点**，并输出最终的判断及理由。

---

## 判断规则
1. 依据弱点定义，判断这个接口是否存在弱点，只需要判断**指定的弱点**。
2. 根据URL、请求头、请求体、响应状态码、响应头、响应体等维度综合分析，特别要根据接口业务属性中和判断。
3. 必须有明确的弱点特征，并且**响应体**为正常返回，否则不要判断为弱点，避免受到扫描流量的影响。

---

## 输出格式
- 返回 JSON 对象，格式如下：
{{
  "reason": "用中文说明接口业务属性，以及判断依据",
  "result": "已忽略/待修复",
  "confidence":"你对自己做出的判断的信心"
}}
"""}]

        self.mongo = MONGO_UTIL
        self.WEAKNESS_MAP = NACOS_UTIL.get_weakness_desc()

    def _init(self):
        self.httpApiWeakness = self.mongo.db.get_collection("httpApiWeakness")
        self.httpSample = self.mongo.db.get_collection("httpSample")
        self.LLMConfig = self.mongo.db.get_collection("LLMConfig")

        # 读取LLM配置
        llm_cfg = self.LLMConfig.find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            return False
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        self.client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        return True

    def get_weakness_sample(self):
        # todo
        pass
        # return (f"<弱点名称>{weakness_name}</弱点名称>\n"
        #         f"<弱点定义>{WEAKNESS_MAP[weakness_name]}</弱点定义>"
        #         f"<弱点ID>{data['operationId']}</弱点ID>"
        #         f"<http请求>{req}</http请求>"
        #         f"<http响应>{rsp}</http响应>")

    def analyze_weakness(self, sample: str, token_usage: dict = {}) -> dict:
        messages = self.system_prompt + [{
            "role": "user",
            "content": f"{sample}"
        }]
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}  # 2025-05-22 deepseek-r1模型 不支持这个参数，暂时注释掉
        )

        message = response.choices[0].message.content
        logger.debug(f"{response.usage.prompt_tokens}+{response.usage.completion_tokens}={response.usage.total_tokens}")
        token_usage['input_tokens'] += response.usage.prompt_tokens
        token_usage['output_tokens'] += response.usage.completion_tokens
        token_usage['total_tokens'] += response.usage.total_tokens

        result = {}
        try:
            result = json.loads(message)
        except:
            logger.error("message is not a json")

        return result

    def run(self, max_uris=5000):
        if not self._init():
            logger.error("初始化失败")
            return

        count = 0
        token_usage = {
            "input_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 0
        }

        for weakness_sample in self.get_weakness_sample():
            if count % 100 == 0:
                logger.debug(f"count: {count}")
            count += 1

            result = self.analyze_weakness(weakness_sample, token_usage)
            # self._save_tag(uri, result)


def run(max_uris=3):  # fixme 为了测试方便改成一次跑3个
    t = HandleWeakness()
    t.run(max_uris)


if __name__ == '__main__':
    run(1)
