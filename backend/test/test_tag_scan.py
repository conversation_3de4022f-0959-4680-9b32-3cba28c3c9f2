
import time
import json
from tasks.tag_scan_api_task import <PERSON><PERSON><PERSON><PERSON>

def get_scan_sample(limit=1000):
    with open("scan_data.json", "r+") as f:
        line_number = 1
        line = f.readline()
        while (line and line_number<limit):
            # print(f"第{line_number}行内容：{line.strip()}")
            yield line.strip()
            line = f.readline()
            line_number += 1

def test_tag_scan(limit=5000):
    h = ScanApi()
    h._init()
    token_usage = {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0
    }
    count = 0
    now = time.time()

    for sample in get_scan_sample(limit):
        count += 1
        sample = json.loads(sample)

        req = sample.get("req", {})
        req = {
            "url": req.get("url"),
            "body": req.get("body", "")[:1000],
            "header": req.get("header"),
            "method": req.get("method")
        }
        rsp = sample.get("rsp", {})
        rsp = {
            "status": rsp.get("status"),
            "header": rsp.get("header"),
            "body": rsp.get("body", "")[:1000]
        }

        prompt = (f"<http请求>{req}</http请求>"
                f"<http响应>{rsp}</http响应>")
        result = h._analyze_api(prompt, token_usage)
        if count % 10 == 0:
            print(f"第{count}个，总token: {token_usage['total_tokens']}=总输入token: {token_usage['input_tokens']}+总输出token: {token_usage['output_tokens']}\n总耗时：{time.time()-now}秒\n")
        print(f"{prompt}\n{result}")

    print(
        f"第{count}个，总token: {token_usage['total_tokens']}=总输入token: {token_usage['input_tokens']}+总输出token: {token_usage['output_tokens']}\n总耗时：{time.time() - now}秒\n")



if __name__ == '__main__':
    test_tag_scan()
