#!/usr/bin/python3
# -*- coding: utf-8 -*-
from tasks.tag_business_api_task import <PERSON><PERSON><PERSON>

def test_tagapi():
    c = TagApi()
    c._init()
    uri = "httpapi:https://1936f3ca-a304-45b7-9cb3-170f836989b7.mock.pstmn.io/cheakPassword"
    sample = c._get_sample(uri)
    # # 请求响应发送给大模型，分析接口，打标
    result = c._analyze_api(sample)
    c._save_tag(uri, result)


if __name__ == '__main__':
    test_tagapi()
